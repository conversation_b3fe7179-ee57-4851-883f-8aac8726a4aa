{"class_fqid": "com-snaplogic-pipeline_9", "snode_id": "68beee811781011942b63771", "instance_id": "9b6e6e93-827a-476f-91cc-4e1fb456421f", "instance_version": 3, "link_map": {"link101": {"dst_id": "d74c92ab-5116-49b8-8919-b553a87477fd", "dst_view_id": "input0", "src_id": "2113aa85-be4e-4c18-a744-eb7862aa19c8", "src_view_id": "output101", "isGoto": false}, "link102": {"dst_id": "031b3594-727f-4608-ba55-6c0ad787445d", "dst_view_id": "input0", "src_id": "ec10f87c-26d3-402f-97c7-5d0e79130600", "src_view_id": "output101", "isGoto": false}, "link103": {"dst_id": "81ffe512-6104-43dd-9bdf-c5697eab5aeb", "dst_view_id": "input0", "src_id": "f28e09b7-24f1-4bc7-9cc1-742286453b7b", "src_view_id": "output101", "isGoto": false}, "link104": {"dst_id": "d35c45d0-3003-4fe6-89bb-45dcd3bbf258", "dst_view_id": "input0", "src_id": "f44c85e9-9911-4a30-ab96-273844dcdb0f", "src_view_id": "output101", "isGoto": false}, "link105": {"dst_id": "e3627cc6-d508-4655-9371-09d84b7c3fc5", "dst_view_id": "input0", "src_id": "76b254de-ebef-4d6b-ac14-e611826b6180", "src_view_id": "output101", "isGoto": false}, "link107": {"dst_id": "07b3fa2c-ff10-4ae4-b6a8-4bd8ddaecbb3", "dst_view_id": "input0", "src_id": "1b34c456-302b-415a-b2bd-6b60d7d3fa20", "src_view_id": "outputRows", "isGoto": false}, "link108": {"dst_id": "38f6843f-e48e-41db-9fd6-e5acfbda1961", "dst_view_id": "input101", "src_id": "07b3fa2c-ff10-4ae4-b6a8-4bd8ddaecbb3", "src_view_id": "output0", "isGoto": false}, "link109": {"dst_id": "f4bfc8b0-6f2c-4666-9f29-68a0d03d3cf1", "dst_view_id": "input101", "src_id": "d74c92ab-5116-49b8-8919-b553a87477fd", "src_view_id": "output0", "isGoto": false}, "link110": {"dst_id": "60254edf-eec4-4281-8d7d-172c75b86fa0", "dst_view_id": "input101", "src_id": "031b3594-727f-4608-ba55-6c0ad787445d", "src_view_id": "output0", "isGoto": false}, "link111": {"dst_id": "025d4b6c-7856-4e4e-8e3b-60b64e2f40a0", "dst_view_id": "input101", "src_id": "81ffe512-6104-43dd-9bdf-c5697eab5aeb", "src_view_id": "output0", "isGoto": false}, "link112": {"dst_id": "33d80986-a8c0-489c-bb82-58d1ad1c29ed", "dst_view_id": "input101", "src_id": "d35c45d0-3003-4fe6-89bb-45dcd3bbf258", "src_view_id": "output0", "isGoto": false}, "link113": {"dst_id": "be2d0f54-a7f5-4b15-81ae-e8cbf16161a3", "dst_view_id": "input101", "src_id": "e3627cc6-d508-4655-9371-09d84b7c3fc5", "src_view_id": "output0", "isGoto": false}}, "link_serial": 116, "property_map": {"info": {"label": {"value": "X02 - Property Test Minmal"}, "author": {"value": "<EMAIL>"}, "pipeline_doc_uri": {"value": null}, "notes": {"value": null}, "purpose": {"value": null}}, "error": {"error_behavior": {"value": "none"}}, "settings": {"error_pipeline": {"value": null, "expression": false}, "error_param_table": {"value": []}, "param_table": {"value": []}, "imports": {"value": []}}, "input": {}, "output": {"38f6843f-e48e-41db-9fd6-e5acfbda1961_output0": {"view_type": {"value": "document"}, "label": {"value": "Create nch_property - output0"}}, "f4bfc8b0-6f2c-4666-9f29-68a0d03d3cf1_output0": {"view_type": {"value": "document"}, "label": {"value": "Create nch_property - output0"}}, "60254edf-eec4-4281-8d7d-172c75b86fa0_output0": {"view_type": {"value": "document"}, "label": {"value": "Create nch_property - output0"}}, "025d4b6c-7856-4e4e-8e3b-60b64e2f40a0_output0": {"view_type": {"value": "document"}, "label": {"value": "Create nch_occupancy - output0"}}, "33d80986-a8c0-489c-bb82-58d1ad1c29ed_output0": {"view_type": {"value": "document"}, "label": {"value": "Create contact - output0"}}, "be2d0f54-a7f5-4b15-81ae-e8cbf16161a3_output0": {"view_type": {"value": "document"}, "label": {"value": "Create nch_occupancypeople - output0"}}}}, "render_map": {"scale_ratio": 1, "pan_x_num": 0, "pan_y_num": 0, "default_snaplex": "5fe335e211b4d4884c3302d9", "detail_map": {"1b34c456-302b-415a-b2bd-6b60d7d3fa20": {"grid_x_int": 3, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "07b3fa2c-ff10-4ae4-b6a8-4bd8ddaecbb3": {"grid_x_int": 4, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "38f6843f-e48e-41db-9fd6-e5acfbda1961": {"grid_x_int": 5, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "d74c92ab-5116-49b8-8919-b553a87477fd": {"grid_x_int": 4, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "f4bfc8b0-6f2c-4666-9f29-68a0d03d3cf1": {"grid_x_int": 5, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "031b3594-727f-4608-ba55-6c0ad787445d": {"grid_x_int": 4, "grid_y_int": 4, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "60254edf-eec4-4281-8d7d-172c75b86fa0": {"grid_x_int": 5, "grid_y_int": 4, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "81ffe512-6104-43dd-9bdf-c5697eab5aeb": {"grid_x_int": 4, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "025d4b6c-7856-4e4e-8e3b-60b64e2f40a0": {"grid_x_int": 5, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "d35c45d0-3003-4fe6-89bb-45dcd3bbf258": {"grid_x_int": 4, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "33d80986-a8c0-489c-bb82-58d1ad1c29ed": {"grid_x_int": 5, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "e3627cc6-d508-4655-9371-09d84b7c3fc5": {"grid_x_int": 4, "grid_y_int": 7, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "be2d0f54-a7f5-4b15-81ae-e8cbf16161a3": {"grid_x_int": 5, "grid_y_int": 7, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "2113aa85-be4e-4c18-a744-eb7862aa19c8": {"grid_x_int": 3, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "ec10f87c-26d3-402f-97c7-5d0e79130600": {"grid_x_int": 3, "grid_y_int": 4, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "f28e09b7-24f1-4bc7-9cc1-742286453b7b": {"grid_x_int": 3, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "f44c85e9-9911-4a30-ab96-273844dcdb0f": {"grid_x_int": 3, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}, "76b254de-ebef-4d6b-ac14-e611826b6180": {"grid_x_int": 3, "grid_y_int": 7, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {}}}}, "snap_map": {"1b34c456-302b-415a-b2bd-6b60d7d3fa20": {"class_fqid": "com-snaplogic-snaps-sqlserver-select_3-main21015", "class_id": "com-snaplogic-snaps-sqlserver-select", "class_version": 3, "instance_fqid": "1b34c456-302b-415a-b2bd-6b60d7d3fa20_1", "instance_id": "1b34c456-302b-415a-b2bd-6b60d7d3fa20", "instance_version": 1, "property_map": {"info": {"label": {"value": "Select BV_Hedyn_Properties"}, "notes": {}}, "output": {"outputRows": {"view_type": {"value": "document"}, "label": {"value": "rows"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"queryHints": {"value": null, "expression": false}, "orderBy": {"value": []}, "autoCommit": {"value": "False"}, "maxRetryProp": {"value": 0, "expression": false}, "execution_mode": {"value": "Validate & Execute"}, "matchDataTypes": {"value": false}, "schemaName": {"value": "dbo", "expression": false}, "limitOffset": {"value": null, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "limitRows": {"value": "10", "expression": false}, "passThrough": {"value": true}, "unique_identifier_column_option": {"value": "Convert to lower case (default)"}, "whereClause": {"value": null, "expression": false}, "ignoreEmptyResult": {"value": false}, "tableName": {"value": "\"dbo\".\"BV_Hedyn_Properties\"", "expression": false}, "fetchOutputFieldsInSchema": {"value": false}, "outputFields": {"value": []}, "stagingMode": {"value": "In memory"}, "rowversion": {"value": false}}, "account": {"account_ref": {"value": {"label": {"value": "OneHousing", "label": "../shared/OneHousing", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "bc047441-3402-45fa-9b8e-2e6e0cc0e115", "expression": false}}}}, "view_serial": 100, "input": {}}, "class_build_tag": "main21015"}, "07b3fa2c-ff10-4ae4-b6a8-4bd8ddaecbb3": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main31019", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "07b3fa2c-ff10-4ae4-b6a8-4bd8ddaecbb3_1", "instance_id": "07b3fa2c-ff10-4ae4-b6a8-4bd8ddaecbb3", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map Property"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"expression": true, "value": "$number_.toString()"}, "targetPath": {"value": "$nch_addressnumber"}}, {"expression": {"value": "$['address##1']", "expression": true}, "targetPath": {"value": "$nch_addressstreet"}}, {"expression": {"value": "$post_code_comp", "expression": true}, "targetPath": {"value": "$nch_postcode"}}, {"expression": {"value": "$['address##1']", "expression": true}, "targetPath": {"value": "$nch_name"}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main31019"}, "38f6843f-e48e-41db-9fd6-e5acfbda1961": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-create_1-main31019", "class_id": "com-snaplogic-snaps-dynamics365forsales-create", "class_version": 1, "instance_fqid": "38f6843f-e48e-41db-9fd6-e5acfbda1961_1", "instance_id": "38f6843f-e48e-41db-9fd6-e5acfbda1961", "instance_version": 1, "property_map": {"info": {"label": {"value": "Create nch_property"}, "notes": {}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": false}, "relatedObjectRelationship": {"value": null, "expression": false}, "relatedObjectType": {"value": null, "expression": false}, "retries": {"value": 0}, "execution_mode": {"value": "Execute only"}, "relatedObjectGuid": {"value": null, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_property", "expression": false}, "executable_during_suggest": {"value": false}, "batchSize": {"value": 200, "expression": false}, "retryDelay": {"value": 1}}, "account": {"account_ref": {"value": {"label": {"value": "NCH D365 UAT", "label": "../shared/NCH D365 UAT", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "ac9388f7-1e3f-4706-b86f-f3307a8665b4", "expression": false}}}}, "view_serial": 101, "input": {"input101": {"label": {"value": "input0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main31019"}, "d74c92ab-5116-49b8-8919-b553a87477fd": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main31019", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "d74c92ab-5116-49b8-8919-b553a87477fd_1", "instance_id": "d74c92ab-5116-49b8-8919-b553a87477fd", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map Street"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$post_code", "expression": true}, "targetPath": {"value": "$nch_postcode"}}, {"expression": {"value": "$street_name", "expression": true}, "targetPath": {"value": "$nch_name"}}, {"expression": {"value": "$street_name", "expression": true}, "targetPath": {"value": "$nch_addressname"}}, {"expression": {"value": "$town", "expression": true}, "targetPath": {"value": "$nch_addressarea"}}, {"expression": {"value": "$street", "expression": true}, "targetPath": {"value": "$nch_unitcode"}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main31019"}, "f4bfc8b0-6f2c-4666-9f29-68a0d03d3cf1": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-create_1-main31019", "class_id": "com-snaplogic-snaps-dynamics365forsales-create", "class_version": 1, "instance_fqid": "f4bfc8b0-6f2c-4666-9f29-68a0d03d3cf1_1", "instance_id": "f4bfc8b0-6f2c-4666-9f29-68a0d03d3cf1", "instance_version": 1, "property_map": {"info": {"label": {"value": "Create nch_property"}, "notes": {}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": false}, "relatedObjectRelationship": {"value": null, "expression": false}, "relatedObjectType": {"value": null, "expression": false}, "retries": {"value": 0}, "execution_mode": {"value": "Execute only"}, "relatedObjectGuid": {"value": null, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_property", "expression": false}, "executable_during_suggest": {"value": false}, "batchSize": {"value": 200, "expression": false}, "retryDelay": {"value": 1}}, "account": {"account_ref": {"value": {"label": {"value": "NCH D365 UAT", "label": "../shared/NCH D365 UAT", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "ac9388f7-1e3f-4706-b86f-f3307a8665b4", "expression": false}}}}, "view_serial": 101, "input": {"input101": {"label": {"value": "input0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main31019"}, "031b3594-727f-4608-ba55-6c0ad787445d": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main31019", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "031b3594-727f-4608-ba55-6c0ad787445d_1", "instance_id": "031b3594-727f-4608-ba55-6c0ad787445d", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map location"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"expression": true, "value": "$number_.toString()"}, "targetPath": {"value": "$nch_addressnumber"}}, {"expression": {"value": "$['address##1']", "expression": true}, "targetPath": {"value": "$nch_addressstreet"}}, {"expression": {"value": "$post_code_comp", "expression": true}, "targetPath": {"value": "$nch_postcode"}}, {"expression": {"value": "$['address##1']", "expression": true}, "targetPath": {"value": "$nch_name"}}, {"expression": {"value": "267a6cb9-9589-f011-b4cb-6045bdfc9d55", "expression": false}, "targetPath": {"value": "$nch_relatedstreet"}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main31019"}, "60254edf-eec4-4281-8d7d-172c75b86fa0": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-create_1-main31019", "class_id": "com-snaplogic-snaps-dynamics365forsales-create", "class_version": 1, "instance_fqid": "60254edf-eec4-4281-8d7d-172c75b86fa0_1", "instance_id": "60254edf-eec4-4281-8d7d-172c75b86fa0", "instance_version": 1, "property_map": {"info": {"label": {"value": "Create nch_property"}, "notes": {}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": false}, "relatedObjectRelationship": {"value": null, "expression": false}, "relatedObjectType": {"value": null, "expression": false}, "retries": {"value": 0}, "execution_mode": {"value": "Execute only"}, "relatedObjectGuid": {"value": null, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_property", "expression": false}, "executable_during_suggest": {"value": false}, "batchSize": {"value": 200, "expression": false}, "retryDelay": {"value": 1}}, "account": {"account_ref": {"value": {"label": {"value": "NCH D365 UAT", "label": "../shared/NCH D365 UAT", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "ac9388f7-1e3f-4706-b86f-f3307a8665b4", "expression": false}}}}, "view_serial": 101, "input": {"input101": {"label": {"value": "input0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main31019"}, "81ffe512-6104-43dd-9bdf-c5697eab5aeb": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main31019", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "81ffe512-6104-43dd-9bdf-c5697eab5aeb_1", "instance_id": "81ffe512-6104-43dd-9bdf-c5697eab5aeb", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map tenancy"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "f86ead6d-9789-f011-b4cc-6045bdf134b1", "expression": false}, "targetPath": {"value": "$nch_property"}}, {"expression": {"value": "$tenancy_ref", "expression": true}, "targetPath": {"value": "$nch_occupancyref"}}, {"expression": {"value": "$tenancy_ref", "expression": true}, "targetPath": {"value": "$nch_occupancyreference"}}, {"expression": {"value": "$tncy_start", "expression": true}, "targetPath": {"value": "$nch_startdate"}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main31019"}, "025d4b6c-7856-4e4e-8e3b-60b64e2f40a0": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-create_1-main31019", "class_id": "com-snaplogic-snaps-dynamics365forsales-create", "class_version": 1, "instance_fqid": "025d4b6c-7856-4e4e-8e3b-60b64e2f40a0_1", "instance_id": "025d4b6c-7856-4e4e-8e3b-60b64e2f40a0", "instance_version": 1, "property_map": {"info": {"label": {"value": "Create nch_occupancy"}, "notes": {}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": false}, "relatedObjectRelationship": {"value": null, "expression": false}, "relatedObjectType": {"value": null, "expression": false}, "retries": {"value": 0}, "execution_mode": {"value": "Execute only"}, "relatedObjectGuid": {"value": null, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_occupancy", "expression": false}, "executable_during_suggest": {"value": false}, "batchSize": {"value": 200, "expression": false}, "retryDelay": {"value": 1}}, "account": {"account_ref": {"value": {"label": {"value": "NCH D365 UAT", "label": "../shared/NCH D365 UAT", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "ac9388f7-1e3f-4706-b86f-f3307a8665b4", "expression": false}}}}, "view_serial": 101, "input": {"input101": {"label": {"value": "input0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main31019"}, "d35c45d0-3003-4fe6-89bb-45dcd3bbf258": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main31019", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "d35c45d0-3003-4fe6-89bb-45dcd3bbf258_1", "instance_id": "d35c45d0-3003-4fe6-89bb-45dcd3bbf258", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map person"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$forenames", "expression": true}, "targetPath": {"value": "$firstname"}}, {"expression": {"value": "$surname", "expression": true}, "targetPath": {"value": "$lastname"}}, {"expression": {"value": "f86ead6d-9789-f011-b4cc-6045bdf134b1", "expression": false}, "targetPath": {"value": "$nch_currentresidence"}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main31019"}, "33d80986-a8c0-489c-bb82-58d1ad1c29ed": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-create_1-main31019", "class_id": "com-snaplogic-snaps-dynamics365forsales-create", "class_version": 1, "instance_fqid": "33d80986-a8c0-489c-bb82-58d1ad1c29ed_1", "instance_id": "33d80986-a8c0-489c-bb82-58d1ad1c29ed", "instance_version": 1, "property_map": {"info": {"label": {"value": "Create contact"}, "notes": {}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": false}, "relatedObjectRelationship": {"value": null, "expression": false}, "relatedObjectType": {"value": null, "expression": false}, "retries": {"value": 0}, "execution_mode": {"value": "Execute only"}, "relatedObjectGuid": {"value": null, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "contact", "expression": false}, "executable_during_suggest": {"value": false}, "batchSize": {"value": 200, "expression": false}, "retryDelay": {"value": 1}}, "account": {"account_ref": {"value": {"label": {"value": "NCH D365 UAT", "label": "../shared/NCH D365 UAT", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "ac9388f7-1e3f-4706-b86f-f3307a8665b4", "expression": false}}}}, "view_serial": 101, "input": {"input101": {"label": {"value": "input0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main31019"}, "e3627cc6-d508-4655-9371-09d84b7c3fc5": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main31019", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "e3627cc6-d508-4655-9371-09d84b7c3fc5_1", "instance_id": "e3627cc6-d508-4655-9371-09d84b7c3fc5", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map occupancy"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "e321145b-9889-f011-b4cb-6045bdfc9fff", "expression": false}, "targetPath": {"value": "$nch_occupancy"}}, {"expression": {"value": "f86ead6d-9789-f011-b4cc-6045bdf134b1", "expression": false}, "targetPath": {"value": "$nch_relatedproperty"}}, {"expression": {"value": "c3713bc9-9989-f011-b4cb-7c1e521e4341", "expression": false}, "targetPath": {"value": "$nch_contact"}}, {"expression": {"value": "$start_date", "expression": true}, "targetPath": {"value": "$nch_dateadded"}}, {"expression": {"value": "$start_date", "expression": true}, "targetPath": {"value": "$nch_startdate"}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main31019"}, "be2d0f54-a7f5-4b15-81ae-e8cbf16161a3": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-create_1-main31019", "class_id": "com-snaplogic-snaps-dynamics365forsales-create", "class_version": 1, "instance_fqid": "be2d0f54-a7f5-4b15-81ae-e8cbf16161a3_1", "instance_id": "be2d0f54-a7f5-4b15-81ae-e8cbf16161a3", "instance_version": 1, "property_map": {"info": {"label": {"value": "Create nch_occupancypeople"}, "notes": {}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": false}, "relatedObjectRelationship": {"value": null, "expression": false}, "relatedObjectType": {"value": null, "expression": false}, "retries": {"value": 0}, "execution_mode": {"value": "Execute only"}, "relatedObjectGuid": {"value": null, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_occupancypeople", "expression": false}, "executable_during_suggest": {"value": false}, "batchSize": {"value": 200, "expression": false}, "retryDelay": {"value": 1}}, "account": {"account_ref": {"value": {"label": {"value": "NCH D365 UAT", "label": "../shared/NCH D365 UAT", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "ac9388f7-1e3f-4706-b86f-f3307a8665b4", "expression": false}}}}, "view_serial": 101, "input": {"input101": {"label": {"value": "input0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main31019"}, "2113aa85-be4e-4c18-a744-eb7862aa19c8": {"class_fqid": "com-snaplogic-snaps-sqlserver-execute_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-execute", "class_version": 1, "instance_fqid": "2113aa85-be4e-4c18-a744-eb7862aa19c8_1", "instance_id": "2113aa85-be4e-4c18-a744-eb7862aa19c8", "instance_version": 1, "property_map": {"info": {"label": {"value": "Select Street"}, "notes": {}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Execute only"}, "executable_during_suggest": {"value": false}, "passThrough": {"value": true}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "autoCommit": {"value": "Use account setting"}, "sqlStatement": {"value": "select * from co_street where street = '320'", "expression": false}, "ignoreEmptyResult": {"value": false}}, "account": {"account_ref": {"value": {"label": {"value": "OneHousing", "label": "../shared/OneHousing", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "bc047441-3402-45fa-9b8e-2e6e0cc0e115", "expression": false}}}}, "view_serial": 101, "input": {}, "output": {"output101": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}, "ec10f87c-26d3-402f-97c7-5d0e79130600": {"class_fqid": "com-snaplogic-snaps-sqlserver-execute_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-execute", "class_version": 1, "instance_fqid": "ec10f87c-26d3-402f-97c7-5d0e79130600_1", "instance_id": "ec10f87c-26d3-402f-97c7-5d0e79130600", "instance_version": 1, "property_map": {"info": {"label": {"value": "Select location"}, "notes": {}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Execute only"}, "executable_during_suggest": {"value": false}, "passThrough": {"value": true}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "autoCommit": {"value": "Use account setting"}, "sqlStatement": {"value": "select * from ih_location left join co_place on ih_location.place_ref = co_place.place_ref where ih_location.place_ref = 8935", "expression": false}, "ignoreEmptyResult": {"value": false}}, "account": {"account_ref": {"value": {"label": {"value": "OneHousing", "label": "../shared/OneHousing", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "bc047441-3402-45fa-9b8e-2e6e0cc0e115", "expression": false}}}}, "view_serial": 101, "input": {}, "output": {"output101": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}, "f28e09b7-24f1-4bc7-9cc1-742286453b7b": {"class_fqid": "com-snaplogic-snaps-sqlserver-execute_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-execute", "class_version": 1, "instance_fqid": "f28e09b7-24f1-4bc7-9cc1-742286453b7b_1", "instance_id": "f28e09b7-24f1-4bc7-9cc1-742286453b7b", "instance_version": 1, "property_map": {"info": {"label": {"value": "Select tenancy "}, "notes": {}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Execute only"}, "executable_during_suggest": {"value": false}, "passThrough": {"value": true}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "autoCommit": {"value": "Use account setting"}, "sqlStatement": {"value": "select * from re_tenancy where tenancy_ref = 16800", "expression": false}, "ignoreEmptyResult": {"value": false}}, "account": {"account_ref": {"value": {"label": {"value": "OneHousing", "label": "../shared/OneHousing", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "bc047441-3402-45fa-9b8e-2e6e0cc0e115", "expression": false}}}}, "view_serial": 101, "input": {}, "output": {"output101": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}, "f44c85e9-9911-4a30-ab96-273844dcdb0f": {"class_fqid": "com-snaplogic-snaps-sqlserver-execute_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-execute", "class_version": 1, "instance_fqid": "f44c85e9-9911-4a30-ab96-273844dcdb0f_1", "instance_id": "f44c85e9-9911-4a30-ab96-273844dcdb0f", "instance_version": 1, "property_map": {"info": {"label": {"value": "Select persons"}, "notes": {}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Execute only"}, "executable_during_suggest": {"value": false}, "passThrough": {"value": true}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "autoCommit": {"value": "Use account setting"}, "sqlStatement": {"value": "select * from co_person where person_ref in ('9499','9500')", "expression": false}, "ignoreEmptyResult": {"value": false}}, "account": {"account_ref": {"value": {"label": {"value": "OneHousing", "label": "../shared/OneHousing", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "bc047441-3402-45fa-9b8e-2e6e0cc0e115", "expression": false}}}}, "view_serial": 101, "input": {}, "output": {"output101": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}, "76b254de-ebef-4d6b-ac14-e611826b6180": {"class_fqid": "com-snaplogic-snaps-sqlserver-execute_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-execute", "class_version": 1, "instance_fqid": "76b254de-ebef-4d6b-ac14-e611826b6180_1", "instance_id": "76b254de-ebef-4d6b-ac14-e611826b6180", "instance_version": 1, "property_map": {"info": {"label": {"value": "Select tncy_person"}, "notes": {}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "executable_during_suggest": {"value": false}, "passThrough": {"value": true}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "autoCommit": {"value": "Use account setting"}, "sqlStatement": {"value": "select * from re_tncy_person where tncy_sys_ref = 1001786", "expression": false}, "ignoreEmptyResult": {"value": false}}, "account": {"account_ref": {"value": {"label": {"value": "OneHousing", "label": "../shared/OneHousing", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "bc047441-3402-45fa-9b8e-2e6e0cc0e115", "expression": false}}}}, "view_serial": 101, "input": {}, "output": {"output101": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}}, "path_id": "/NCH-prod/D365 - Integration/Properties-DEV", "path_snode": "67b46f275f364cca45087345"}